"""
Route registry management for HTTP routes
"""

import asyncio
from typing import Callable, Dict, List, Optional

from ..logging import get_logger

logger = get_logger(__name__)


class SystemRouteRegistry:
    """Registry for managing system HTTP routes (not database-dependent)"""

    def __init__(self):
        self._routes: Dict[str, Dict] = {}  # path -> route_info
        self._route_handlers: List[Callable] = []
        self._method_routes: Dict[str, List[Dict]] = {}  # path -> list of method-specific routes

    def register_route(self, path: str, handler: Callable, **kwargs):
        """Register a system route handler"""
        methods = kwargs.get('methods', ['GET'])
        route_info = {
            'handler': handler,
            'path': path,
            **kwargs
        }

        # Store in method-specific routes for FastAPI registration
        if path not in self._method_routes:
            self._method_routes[path] = []
        self._method_routes[path].append(route_info)

        # For backward compatibility, store the latest route in main routes dict
        # This will be used by get_routes() for simple access
        self._routes[path] = route_info
        self._route_handlers.append(handler)
        logger.debug(f"Registered system route: {methods} {path}")

    def get_routes(self) -> Dict[str, Dict]:
        """Get all registered system routes (latest per path for backward compatibility)"""
        return self._routes.copy()

    def get_method_routes(self) -> Dict[str, List[Dict]]:
        """Get all routes organized by path with method-specific entries"""
        return self._method_routes.copy()

    def get_route_handlers(self) -> List[Callable]:
        """Get all system route handlers"""
        return self._route_handlers.copy()

    def clear(self):
        """Clear all registered routes"""
        self._routes.clear()
        self._route_handlers.clear()
        self._method_routes.clear()


class DatabaseRouteManager:
    """Manager for database-specific route operations"""
    
    @staticmethod
    def register_route_in_database_registry(path: str, handler: Callable, **kwargs) -> bool:
        """
        Optimized route registration for database-specific registry
        Returns True if successfully scheduled, False otherwise
        """
        try:
            from ..context import ContextManager

            # Get current database from context
            db_name = ContextManager.get_database()
            if not db_name:
                return False

            # Check if we're in an async context
            try:
                loop = asyncio.get_running_loop()
                # Schedule registration without creating unnecessary tasks
                loop.call_soon_threadsafe(
                    DatabaseRouteManager._schedule_route_registration, 
                    db_name, path, handler, kwargs
                )
                return True
            except RuntimeError:
                return False

        except Exception as e:
            logger.debug(f"Failed to schedule route registration: {e}")
            return False

    @staticmethod
    def _schedule_route_registration(db_name: str, path: str, handler: Callable, kwargs: dict):
        """Schedule route registration for later execution"""
        async def register_async():
            try:
                from ..database.memory import MemoryRegistryManager
                if await MemoryRegistryManager._is_base_module_installed(db_name):
                    registry = await MemoryRegistryManager.get_registry(db_name)
                    await registry.register_route(path, handler, **kwargs)
            except Exception as e:
                logger.debug(f"Failed to register route for {db_name}: {e}")

        asyncio.create_task(register_async())

    @staticmethod
    async def get_database_route_registry(db_name: str):
        """Get route registry for a specific database"""
        from ..database.memory import MemoryRegistryManager

        # Only get registry if base module is installed to avoid issues during initialization
        if await MemoryRegistryManager._is_base_module_installed(db_name):
            registry = await MemoryRegistryManager.get_registry(db_name)
            return registry
        else:
            logger.debug(f"Cannot get route registry for {db_name} - base module not installed yet")
            return None

    @staticmethod
    async def get_all_database_routes() -> Dict[str, Dict[str, Dict]]:
        """Get routes from all database registries"""
        from ..database.memory import MemoryRegistryManager
        
        all_registries = await MemoryRegistryManager.get_all_registries()
        database_routes = {}
        
        for db_name, registry in all_registries.items():
            routes = await registry.get_routes()
            if routes:
                database_routes[db_name] = routes
        
        return database_routes

    @staticmethod
    async def register_route_for_database(db_name: str, path: str, handler: Callable, **kwargs):
        """
        Optimized route registration for a specific database

        Args:
            db_name: Database name
            path: Route path
            handler: Route handler function
            **kwargs: Route metadata
        """
        from ..database.memory import MemoryRegistryManager

        try:
            if await MemoryRegistryManager._is_base_module_installed(db_name):
                registry = await MemoryRegistryManager.get_registry(db_name)
                await registry.register_route(path, handler, **kwargs)
                logger.info(f"Registered route for {db_name}: {kwargs.get('methods', ['GET'])} {path}")
            else:
                logger.debug(f"Skipping route registration for {db_name} - base module not installed")
        except Exception as e:
            logger.error(f"Failed to register route for {db_name}: {e}")


# Global system route registry instance
_system_route_registry = SystemRouteRegistry()


def get_system_route_registry() -> SystemRouteRegistry:
    """Get the global system route registry"""
    return _system_route_registry


def reset_system_route_registry():
    """Reset the global system route registry (mainly for testing)"""
    global _system_route_registry
    _system_route_registry = SystemRouteRegistry()


# Convenience functions for database route management
async def get_database_route_registry(db_name: str):
    """Get route registry for a specific database"""
    return await DatabaseRouteManager.get_database_route_registry(db_name)


async def get_all_database_routes() -> Dict[str, Dict[str, Dict]]:
    """Get routes from all database registries"""
    return await DatabaseRouteManager.get_all_database_routes()


async def register_route_for_database(db_name: str, path: str, handler: Callable, **kwargs):
    """Register route for a specific database"""
    return await DatabaseRouteManager.register_route_for_database(db_name, path, handler, **kwargs)
